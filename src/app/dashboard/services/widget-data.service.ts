import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class WidgetDataService {
  getStaffOnShift(): Observable<number> {
    return of(5); // Mocked value
  }

  getUpcomingShifts(): Observable<any[]> {
    return of([
      { id: '1', startTime: '08:00', endTime: '12:00', staffName: 'Alice' },
      { id: '2', startTime: '12:00', endTime: '16:00', staffName: 'Bob' },
      { id: '3', startTime: '16:00', endTime: '20:00', staffName: 'Charlie' }
    ]);
  }

  getNotifications(): Observable<number> {
    return of(2); // Mocked value
  }

  getMessages(): Observable<number> {
    return of(1); // Mocked value
  }
} 