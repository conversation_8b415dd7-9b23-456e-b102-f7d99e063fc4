@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.enhanced-dashboard {
  min-height: 100vh;
  background: var(--sm-background-default, #f7f9fa);
  padding: var(--sm-spacing-lg, 24px);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.3s ease;

  &.dark-theme {
    background: var(--sm-background-default, #121212);
    color: var(--sm-text-primary, #ffffff);
  }
}

// Dashboard Header
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--sm-spacing-lg, 24px);
  padding: var(--sm-spacing-md, 16px) 0;

  .header-left {
    flex: 1;
  }

  .dashboard-title {
    display: flex;
    align-items: center;
    gap: var(--sm-spacing-sm, 8px);
    margin: 0 0 var(--sm-spacing-sm, 8px) 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--sm-text-primary, #1a1a1a);
    
    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: var(--sm-primary-main, #1976d2);
    }
  }

  .dashboard-subtitle {
    margin: 0;
    font-size: 1rem;
    color: var(--sm-text-secondary, #666666);
    font-weight: 400;
  }

  .header-actions {
    display: flex;
    gap: var(--sm-spacing-sm, 8px);
    align-items: center;

    button {
      transition: all 0.2s ease;
      
      &.active {
        background-color: var(--sm-primary-main, #1976d2);
        color: white;
      }

      &:hover:not(:disabled) {
        background-color: rgba(25, 118, 210, 0.1);
      }

      &:disabled {
        opacity: 0.5;
      }
    }
  }
}

// Edit Mode Banner
.edit-mode-banner {
  display: flex;
  align-items: center;
  gap: var(--sm-spacing-sm, 8px);
  padding: var(--sm-spacing-md, 16px);
  margin-bottom: var(--sm-spacing-lg, 24px);
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid var(--sm-primary-main, #1976d2);
  border-radius: var(--sm-border-radius-medium, 8px);
  color: var(--sm-primary-dark, #1565c0);
  font-weight: 500;
  animation: slideIn 0.3s ease;

  mat-icon {
    color: var(--sm-primary-main, #1976d2);
  }

  button {
    margin-left: auto;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Widgets Container
.widgets-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--sm-spacing-lg, 24px);
  min-height: 400px;
}

// Widget Wrapper
.widget-wrapper {
  position: relative;
  background: var(--sm-background-paper, #ffffff);
  border-radius: var(--sm-border-radius-medium, 8px);
  box-shadow: var(--sm-shadow-light, 0 2px 4px rgba(0,0,0,0.1));
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: var(--sm-shadow-medium, 0 4px 8px rgba(0,0,0,0.12));
    transform: translateY(-2px);
  }

  &.edit-mode {
    border: 2px dashed var(--sm-primary-main, #1976d2);
    
    &:hover {
      border-color: var(--sm-primary-dark, #1565c0);
      box-shadow: var(--sm-shadow-heavy, 0 8px 16px rgba(0,0,0,0.15));
    }
  }

  &.cdk-drag-preview {
    box-shadow: var(--sm-shadow-heavy, 0 8px 16px rgba(0,0,0,0.15));
    transform: rotate(5deg);
  }

  &.cdk-drag-placeholder {
    opacity: 0.3;
    border: 2px dashed var(--sm-primary-light, #42a5f5);
  }

  // Size variants
  &.size-small {
    grid-column: span 1;
    min-height: 150px;
  }

  &.size-medium {
    grid-column: span 1;
    min-height: 200px;
  }

  &.size-large {
    grid-column: span 2;
    min-height: 300px;
  }
}

// Widget Header (Edit Mode)
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--sm-spacing-sm, 8px) var(--sm-spacing-md, 16px);
  background: rgba(25, 118, 210, 0.05);
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);

  .drag-handle {
    cursor: grab;
    color: var(--sm-primary-main, #1976d2);
    
    &:active {
      cursor: grabbing;
    }

    mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }

  .widget-actions {
    display: flex;
    gap: var(--sm-spacing-xs, 4px);

    button {
      width: 32px;
      height: 32px;
      line-height: 32px;
      
      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Widget Content
.widget-content {
  padding: var(--sm-spacing-md, 16px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Widget Placeholder
.widget-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 120px;
  color: var(--sm-text-secondary, #666666);
  text-align: center;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: var(--sm-spacing-sm, 8px);
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 var(--sm-spacing-xs, 4px) 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.7;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .enhanced-dashboard {
    padding: var(--sm-spacing-md, 16px);
  }

  .dashboard-header {
    flex-direction: column;
    gap: var(--sm-spacing-md, 16px);
    align-items: stretch;

    .header-actions {
      justify-content: center;
    }
  }

  .widgets-container {
    grid-template-columns: 1fr;
    gap: var(--sm-spacing-md, 16px);
  }

  .widget-wrapper {
    &.size-large {
      grid-column: span 1;
    }
  }

  .dashboard-title {
    font-size: 1.5rem;
    
    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

@media (max-width: 480px) {
  .enhanced-dashboard {
    padding: var(--sm-spacing-sm, 8px);
  }

  .edit-mode-banner {
    flex-direction: column;
    text-align: center;
    gap: var(--sm-spacing-sm, 8px);

    button {
      margin-left: 0;
    }
  }
}

// Dark theme specific styles
.dark-theme {
  .widget-wrapper {
    background: var(--sm-background-paper, #1e1e1e);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .widget-header {
    background: rgba(66, 165, 245, 0.1);
    border-bottom-color: rgba(66, 165, 245, 0.2);
  }

  .edit-mode-banner {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(186, 104, 200, 0.1) 100%);
    border-color: var(--sm-primary-light, #42a5f5);
    color: var(--sm-primary-light, #42a5f5);
  }
}
