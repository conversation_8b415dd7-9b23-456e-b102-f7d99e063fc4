@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.enhanced-dashboard {
  min-height: 100vh;
  background: var(--sm-background-default, #f7f9fa);
  padding: 24px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.3s ease;

  &.dark-theme {
    background: var(--sm-background-default, #121212);
    color: var(--sm-text-primary, #ffffff);
  }
}

// Dashboard Header
.dashboard-header {
  margin-bottom: 32px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0;
  }

  .header-left {
    flex: 1;
  }

  .dashboard-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 8px 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--sm-text-primary, #1a1a1a);
    letter-spacing: -0.025em;

    .dashboard-icon {
      font-size: 1.75rem;
      width: 1.75rem;
      height: 1.75rem;
      color: var(--sm-primary-main, #1976d2);
    }
  }

  .dashboard-subtitle {
    margin: 0;
    font-size: 1rem;
    color: var(--sm-text-secondary, #666666);
    font-weight: 400;
    line-height: 1.5;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .action-btn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      transition: all 0.2s ease;

      &.active {
        background-color: var(--sm-primary-main, #1976d2);
        color: white;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      }

      &:hover:not(:disabled) {
        background-color: rgba(25, 118, 210, 0.08);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Dashboard Stats Grid
.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  }

  &.staff-card::before {
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  }

  &.shifts-card::before {
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  }

  &.notifications-card::before {
    background: linear-gradient(90deg, #ff6b35 0%, #ff8a65 100%);
  }

  &.messages-card::before {
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  }
}

.stat-content {
  padding: 24px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  letter-spacing: -0.01em;
}

.stat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: var(--sm-primary-main, #1976d2);
  opacity: 0.8;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--sm-primary-main, #1976d2);
  line-height: 1;
  margin-bottom: 8px;
  letter-spacing: -0.02em;
}

.stat-description {
  font-size: 0.95rem;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

.stat-schedule {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 0.9rem;

  &:not(:last-child) {
    border-bottom: 1px solid #f8f8f8;
  }
}

.schedule-time {
  font-weight: 600;
  color: var(--sm-primary-main, #1976d2);
  min-width: 80px;
}

.schedule-name {
  color: #333;
  font-weight: 500;
}

// Edit Mode Banner
.edit-mode-banner {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid var(--sm-primary-main, #1976d2);
  border-radius: 12px;
  color: var(--sm-primary-dark, #1565c0);
  font-weight: 500;
  animation: slideIn 0.3s ease;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);

  mat-icon {
    color: var(--sm-primary-main, #1976d2);
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  button {
    margin-left: auto;
    border-radius: 8px;
    font-weight: 600;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Widgets Container (Edit Mode)
.widgets-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  min-height: 400px;
  padding: 20px;
  background: rgba(25, 118, 210, 0.02);
  border-radius: 12px;
  border: 2px dashed rgba(25, 118, 210, 0.2);
}

// Widget Wrapper
.widget-wrapper {
  position: relative;
  background: var(--sm-background-paper, #ffffff);
  border-radius: var(--sm-border-radius-medium, 8px);
  box-shadow: var(--sm-shadow-light, 0 2px 4px rgba(0,0,0,0.1));
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: var(--sm-shadow-medium, 0 4px 8px rgba(0,0,0,0.12));
    transform: translateY(-2px);
  }

  &.edit-mode {
    border: 2px dashed var(--sm-primary-main, #1976d2);

    &:hover {
      border-color: var(--sm-primary-dark, #1565c0);
      box-shadow: var(--sm-shadow-heavy, 0 8px 16px rgba(0,0,0,0.15));
    }
  }

  &.cdk-drag-preview {
    box-shadow: var(--sm-shadow-heavy, 0 8px 16px rgba(0,0,0,0.15));
    transform: rotate(5deg);
  }

  &.cdk-drag-placeholder {
    opacity: 0.3;
    border: 2px dashed var(--sm-primary-light, #42a5f5);
  }

  // Size variants
  &.size-small {
    grid-column: span 1;
    min-height: 150px;
  }

  &.size-medium {
    grid-column: span 1;
    min-height: 200px;
  }

  &.size-large {
    grid-column: span 2;
    min-height: 300px;
  }
}

// Widget Header (Edit Mode)
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--sm-spacing-sm, 8px) var(--sm-spacing-md, 16px);
  background: rgba(25, 118, 210, 0.05);
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);

  .drag-handle {
    cursor: grab;
    color: var(--sm-primary-main, #1976d2);

    &:active {
      cursor: grabbing;
    }

    mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }

  .widget-actions {
    display: flex;
    gap: var(--sm-spacing-xs, 4px);

    button {
      width: 32px;
      height: 32px;
      line-height: 32px;

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Widget Content
.widget-content {
  padding: var(--sm-spacing-md, 16px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Widget Placeholder
.widget-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 120px;
  color: var(--sm-text-secondary, #666666);
  text-align: center;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: var(--sm-spacing-sm, 8px);
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 var(--sm-spacing-xs, 4px) 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.7;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .enhanced-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .header-actions {
      justify-content: center;
    }
  }

  .dashboard-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .widgets-container {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .widget-wrapper {
    &.size-large {
      grid-column: span 1;
    }
  }

  .dashboard-title {
    font-size: 1.5rem;

    .dashboard-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  .stat-content {
    padding: 20px;
  }

  .stat-value {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .enhanced-dashboard {
    padding: 12px;
  }

  .dashboard-stats-grid {
    gap: 12px;
  }

  .stat-content {
    padding: 16px;
  }

  .stat-value {
    font-size: 1.8rem;
  }

  .edit-mode-banner {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    padding: 12px 16px;

    button {
      margin-left: 0;
    }
  }

  .header-actions {
    .action-btn {
      width: 36px;
      height: 36px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Dark theme specific styles
.dark-theme {
  .stat-card {
    background: var(--sm-background-paper, #1e1e1e);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;

    .stat-title {
      color: #ffffff;
    }

    .stat-description {
      color: #b3b3b3;
    }

    .schedule-item {
      border-bottom-color: rgba(255, 255, 255, 0.1);

      .schedule-time {
        color: var(--sm-primary-light, #42a5f5);
      }

      .schedule-name {
        color: #ffffff;
      }
    }

    .stat-schedule {
      border-top-color: rgba(255, 255, 255, 0.1);
    }
  }

  .widget-wrapper {
    background: var(--sm-background-paper, #1e1e1e);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .widget-header {
    background: rgba(66, 165, 245, 0.1);
    border-bottom-color: rgba(66, 165, 245, 0.2);
  }

  .edit-mode-banner {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(186, 104, 200, 0.1) 100%);
    border-color: var(--sm-primary-light, #42a5f5);
    color: var(--sm-primary-light, #42a5f5);
  }

  .widgets-container {
    background: rgba(66, 165, 245, 0.05);
    border-color: rgba(66, 165, 245, 0.2);
  }
}
