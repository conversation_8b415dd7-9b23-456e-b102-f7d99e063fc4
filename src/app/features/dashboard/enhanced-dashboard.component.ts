import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, Observable } from 'rxjs';

import { NumberWidgetComponent } from '../../dashboard/widgets/number-widget.component';
import { UpcomingShiftsWidgetComponent } from '../../dashboard/widgets/upcoming-shifts-widget.component';
import { DashboardStateService, DashboardWidget } from '../../dashboard/services/dashboard-state.service';
import { WidgetDataService } from '../../dashboard/services/widget-data.service';
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';

export interface EnhancedWidget extends DashboardWidget {
  type: 'number' | 'shifts' | 'chart' | 'list' | 'custom';
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  data?: any;
  config?: any;
}

@Component({
  selector: 'app-enhanced-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    MatGridListModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatTooltipModule,
    MatSnackBarModule,
    NumberWidgetComponent,
    UpcomingShiftsWidgetComponent
  ],
  template: `
    <div class="enhanced-dashboard" [class.dark-theme]="themeService.isDark()">
      <!-- Dashboard Header -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="dashboard-title">
              <mat-icon class="dashboard-icon">dashboard</mat-icon>
              StaffManager Dashboard
            </h1>
            <p class="dashboard-subtitle">Welcome back! Here's what's happening today.</p>
          </div>

          <div class="header-actions">
            <button mat-icon-button
                    [matTooltip]="editMode ? 'Exit Edit Mode' : 'Enter Edit Mode'"
                    (click)="toggleEditMode()"
                    [class.active]="editMode"
                    class="action-btn">
              <mat-icon>{{ editMode ? 'edit_off' : 'edit' }}</mat-icon>
            </button>

            <button mat-icon-button
                    matTooltip="Add Widget"
                    [disabled]="!editMode"
                    [matMenuTriggerFor]="addWidgetMenu"
                    class="action-btn">
              <mat-icon>add_circle</mat-icon>
            </button>

            <button mat-icon-button
                    matTooltip="Refresh Data"
                    (click)="refreshData()"
                    class="action-btn">
              <mat-icon>refresh</mat-icon>
            </button>

            <button mat-icon-button
                    matTooltip="Toggle Theme"
                    (click)="toggleTheme()"
                    class="action-btn">
              <mat-icon>{{ themeService.isDark() ? 'light_mode' : 'dark_mode' }}</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Edit Mode Banner -->
      <div class="edit-mode-banner" *ngIf="editMode">
        <mat-icon>info</mat-icon>
        <span>Edit Mode: Drag widgets to rearrange, click settings to configure</span>
        <button mat-button color="primary" (click)="saveLayout()">Save Layout</button>
      </div>

      <!-- Dashboard Cards Grid -->
      <div class="dashboard-cards-grid">
        <!-- Staff On Shift Card -->
        <mat-card class="dashboard-card staff-card">
          <div class="card-header">
            <h3 class="card-title">Staff On Shift</h3>
            <div class="card-actions">
              <button mat-icon-button><mat-icon>refresh</mat-icon></button>
              <button mat-icon-button><mat-icon>more_vert</mat-icon></button>
            </div>
          </div>
          <div class="card-number">5</div>
          <div class="card-subtitle">Staff members currently on shift</div>

          <div class="card-content">
            <div class="section-title">Currently Working:</div>
            <ul class="staff-list">
              <li class="staff-item">
                <span class="staff-name">John Doe</span>
                <span class="staff-role">(Front Desk)</span>
              </li>
              <li class="staff-item">
                <span class="staff-name">Jane Smith</span>
                <span class="staff-role">(Manager)</span>
              </li>
              <li class="staff-item">
                <span class="staff-name">Bob Johnson</span>
                <span class="staff-role">(Technician)</span>
              </li>
              <li class="staff-item">
                <span class="staff-name">Alice Williams</span>
                <span class="staff-role">(Support)</span>
              </li>
              <li class="staff-item">
                <span class="staff-name">Charlie Brown</span>
                <span class="staff-role">(Sales)</span>
              </li>
            </ul>
          </div>
        </mat-card>

        <!-- Upcoming Shifts Card -->
        <mat-card class="dashboard-card shifts-card">
          <div class="card-header">
            <h3 class="card-title">Upcoming Shifts</h3>
            <div class="card-actions">
              <button mat-icon-button><mat-icon>refresh</mat-icon></button>
              <button mat-icon-button><mat-icon>more_vert</mat-icon></button>
            </div>
          </div>
          <div class="card-number">8</div>
          <div class="card-subtitle">Upcoming shifts today</div>

          <div class="card-content">
            <div class="section-title">Today's Schedule:</div>
            <ul class="schedule-list">
              <li class="schedule-item">
                <span class="schedule-time">9:00 AM - 5:00 PM:</span>
                <span class="schedule-staff">John Doe</span>
              </li>
              <li class="schedule-item">
                <span class="schedule-time">10:00 AM - 6:00 PM:</span>
                <span class="schedule-staff">Jane Smith</span>
              </li>
              <li class="schedule-item">
                <span class="schedule-time">12:00 PM - 8:00 PM:</span>
                <span class="schedule-staff">Bob Johnson</span>
              </li>
              <li class="schedule-item">
                <span class="schedule-time">2:00 PM - 10:00 PM:</span>
                <span class="schedule-staff">Alice Williams</span>
              </li>
              <li class="schedule-item">
                <span class="schedule-time">4:00 PM - 12:00 AM:</span>
                <span class="schedule-staff">Charlie Brown</span>
              </li>
              <li class="schedule-item">
                <span class="schedule-time">6:00 PM - 2:00 AM:</span>
                <span class="schedule-staff">David Miller</span>
              </li>
            </ul>
          </div>
        </mat-card>

        <!-- Notifications Card -->
        <mat-card class="dashboard-card notifications-card">
          <div class="card-header">
            <h3 class="card-title">Notifications</h3>
            <div class="card-actions">
              <button mat-icon-button><mat-icon>refresh</mat-icon></button>
              <button mat-icon-button><mat-icon>more_vert</mat-icon></button>
            </div>
          </div>
          <div class="card-number">3</div>
          <div class="card-subtitle">New notifications</div>

          <div class="card-content">
            <div class="notification-item">
              <div class="notification-title">New schedule published</div>
              <div class="notification-time">10 minutes ago</div>
            </div>
            <div class="notification-item">
              <div class="notification-title">Staff shortage on Friday</div>
              <div class="notification-time">1 hour ago</div>
            </div>
            <div class="notification-item">
              <div class="notification-title">Urgent: System maintenance</div>
              <div class="notification-time">2 hours ago</div>
            </div>
          </div>
        </mat-card>

        <!-- Messages Card -->
        <mat-card class="dashboard-card messages-card">
          <div class="card-header">
            <h3 class="card-title">Messages</h3>
            <div class="card-actions">
              <button mat-icon-button><mat-icon>refresh</mat-icon></button>
              <button mat-icon-button><mat-icon>more_vert</mat-icon></button>
            </div>
          </div>
          <div class="card-number">7</div>
          <div class="card-subtitle">Unread messages</div>

          <div class="card-content">
            <div class="message-item">
              <div class="message-title">Schedule change request</div>
              <div class="message-meta">From: John Doe • 5 minutes ago</div>
            </div>
            <div class="message-item">
              <div class="message-title">Team meeting tomorrow</div>
              <div class="message-meta">From: Jane Smith • 30 minutes ago</div>
            </div>
            <div class="message-item">
              <div class="message-title">Equipment maintenance</div>
              <div class="message-meta">From: Bob Johnson • 1 hour ago</div>
            </div>
            <div class="message-item">
              <div class="message-title">New client onboarding</div>
              <div class="message-meta">From: Alice Williams • 2 hours ago</div>
            </div>
          </div>
        </mat-card>
      </div>

      <!-- Edit Mode Widgets Container (when in edit mode) -->
      <div class="widgets-container"
           *ngIf="editMode"
           cdkDropList
           (cdkDropListDropped)="onWidgetDrop($event)">

        <div class="widget-wrapper"
             *ngFor="let widget of enhancedWidgets; trackBy: trackWidget"
             cdkDrag
             [cdkDragDisabled]="!editMode"
             [class.edit-mode]="editMode"
             [ngClass]="'size-' + widget.size">

          <!-- Widget Header (Edit Mode) -->
          <div class="widget-header">
            <div class="drag-handle" cdkDragHandle>
              <mat-icon>drag_indicator</mat-icon>
            </div>
            <div class="widget-actions">
              <button mat-icon-button
                      matTooltip="Widget Settings"
                      (click)="openWidgetSettings(widget)">
                <mat-icon>settings</mat-icon>
              </button>
              <button mat-icon-button
                      matTooltip="Remove Widget"
                      (click)="removeWidget(widget)">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </div>

          <!-- Widget Content -->
          <div class="widget-content">
            <ng-container [ngSwitch]="widget.id">
              <app-number-widget
                *ngSwitchCase="'staff-on-shift'"
                [title]="'Staff On Shift'"
                [value]="(staffOnShift$ | async) ?? 0"
                [description]="'Staff members currently on shift'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <app-upcoming-shifts-widget
                *ngSwitchCase="'upcoming-shifts'"
                [title]="'Upcoming Shifts'"
                [value]="((upcomingShifts$ | async)?.length ?? 0)"
                [description]="'Upcoming shifts today'"
                [shifts]="(upcomingShifts$ | async) ?? []">
              </app-upcoming-shifts-widget>

              <app-number-widget
                *ngSwitchCase="'notifications'"
                [title]="'Notifications'"
                [value]="(notifications$ | async) ?? 0"
                [description]="'Unread notifications'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <app-number-widget
                *ngSwitchCase="'messages'"
                [title]="'Messages'"
                [value]="(messages$ | async) ?? 0"
                [description]="'Unread messages'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <!-- Placeholder for unknown widgets -->
              <div *ngSwitchDefault class="widget-placeholder">
                <mat-icon>widgets</mat-icon>
                <h3>{{ widget.title }}</h3>
                <p>Widget type: {{ widget.type }}</p>
              </div>
            </ng-container>
          </div>
        </div>
      </div>

      <!-- Add Widget Menu -->
      <mat-menu #addWidgetMenu="matMenu">
        <button mat-menu-item (click)="addWidget('staff-count')">
          <mat-icon>people</mat-icon>
          <span>Staff Count Widget</span>
        </button>
        <button mat-menu-item (click)="addWidget('schedule')">
          <mat-icon>schedule</mat-icon>
          <span>Schedule Widget</span>
        </button>
        <button mat-menu-item (click)="addWidget('tasks')">
          <mat-icon>assignment</mat-icon>
          <span>Tasks Widget</span>
        </button>
        <button mat-menu-item (click)="addWidget('notifications')">
          <mat-icon>notifications</mat-icon>
          <span>Notifications Widget</span>
        </button>
      </mat-menu>
    </div>
  `,
  styleUrls: ['./enhanced-dashboard.component.scss']
})
export class EnhancedDashboardComponent implements OnInit, OnDestroy {
  enhancedWidgets: EnhancedWidget[] = [];
  editMode = false;
  private subscription = new Subscription();

  // Data observables
  staffOnShift$: Observable<number>;
  upcomingShifts$: Observable<any[]>;
  notifications$: Observable<number>;
  messages$: Observable<number>;

  constructor(
    private dashboardState: DashboardStateService,
    private widgetData: WidgetDataService,
    public themeService: StaffManagerThemeService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    // Initialize data streams
    this.staffOnShift$ = this.widgetData.getStaffOnShift();
    this.upcomingShifts$ = this.widgetData.getUpcomingShifts();
    this.notifications$ = this.widgetData.getNotifications();
    this.messages$ = this.widgetData.getMessages();
  }

  ngOnInit(): void {
    this.loadEnhancedWidgets();
    this.subscription.add(
      this.dashboardState.editMode$.subscribe(mode => this.editMode = mode)
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private loadEnhancedWidgets(): void {
    // Convert basic widgets to enhanced widgets with positioning and sizing
    const basicWidgets = [
      { id: 'staff-on-shift', title: 'Staff On Shift' },
      { id: 'upcoming-shifts', title: 'Upcoming Shifts' },
      { id: 'notifications', title: 'Notifications' },
      { id: 'messages', title: 'Messages' }
    ];

    this.enhancedWidgets = basicWidgets.map((widget, index) => ({
      ...widget,
      type: widget.id === 'upcoming-shifts' ? 'shifts' : 'number',
      size: widget.id === 'upcoming-shifts' ? 'large' : 'medium',
      position: { x: (index % 2) * 300, y: Math.floor(index / 2) * 200 }
    })) as EnhancedWidget[];
  }

  toggleEditMode(): void {
    this.editMode = !this.editMode;
    this.dashboardState.setEditMode(this.editMode);

    const message = this.editMode ? 'Edit mode enabled' : 'Edit mode disabled';
    this.snackBar.open(message, 'Close', { duration: 2000 });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  refreshData(): void {
    // Trigger data refresh
    this.snackBar.open('Data refreshed', 'Close', { duration: 2000 });
  }

  onWidgetDrop(event: CdkDragDrop<EnhancedWidget[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(this.enhancedWidgets, event.previousIndex, event.currentIndex);
      this.saveLayout();
    }
  }

  addWidget(type: string): void {
    const newWidget: EnhancedWidget = {
      id: `${type}-${Date.now()}`,
      title: `New ${type} Widget`,
      type: 'number',
      size: 'medium',
      position: { x: 0, y: 0 }
    };

    this.enhancedWidgets.push(newWidget);
    this.snackBar.open('Widget added', 'Close', { duration: 2000 });
  }

  removeWidget(widget: EnhancedWidget): void {
    const index = this.enhancedWidgets.findIndex(w => w.id === widget.id);
    if (index > -1) {
      this.enhancedWidgets.splice(index, 1);
      this.snackBar.open('Widget removed', 'Close', { duration: 2000 });
    }
  }

  openWidgetSettings(widget: EnhancedWidget): void {
    // Import and open widget settings dialog
    import('../../dashboard/widget-settings-dialog.component').then(({ WidgetSettingsDialogComponent }) => {
      const dialogRef = this.dialog.open(WidgetSettingsDialogComponent, {
        data: { settings: widget.config || {} },
        width: '400px'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          widget.config = result;
          this.saveLayout();
        }
      });
    });
  }

  saveLayout(): void {
    // Save widget layout to localStorage
    localStorage.setItem('enhanced-dashboard-layout', JSON.stringify(this.enhancedWidgets));
    this.snackBar.open('Layout saved', 'Close', { duration: 2000 });
  }

  trackWidget(index: number, widget: EnhancedWidget): string {
    return widget.id;
  }
}
