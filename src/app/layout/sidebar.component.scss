@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

.mat-nav-list {
  padding-top: 16px;
}

.mat-list-item.active {
  background: #e3eaf2;
  color: #1976d2;
  font-weight: 600;
}

.mat-list-item {
  border-radius: 6px;
  margin-bottom: 4px;
  transition: background 0.2s;
}

.mat-list-item:hover {
  background: #f0f4f8;
}

.mat-icon {
  margin-right: 16px;
}

.sidebar-nav {
  width: 240px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 2px 0 20px rgba(33, 150, 243, 0.1);
  border-right: 1px solid rgba(25, 118, 210, 0.1);
  overflow: hidden;
  padding: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #1976d2 0%, #42a5f5 100%);
  }
}

// Logo Section
.sidebar-logo {
  display: flex;
  align-items: center;
  padding: 20px 16px;
  gap: 12px;
  background: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  letter-spacing: 0.5px;
}

.logo-subtitle {
  font-size: 0.6rem;
  font-weight: 600;
  color: white;
  line-height: 1;
  letter-spacing: 1px;
  margin-top: -2px;
}

.company-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 0.01em;
}

// Collapsed Logo
.sidebar-logo-collapsed {
  display: flex;
  justify-content: center;
  padding: 16px 8px;
  margin-bottom: 8px;
}

.logo-text-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #2c3e50;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 700;
  color: white;
  letter-spacing: 0.5px;
}

.sidebar-divider {
  margin: 8px 0;
}

.sidebar-section-label {
  font-size: 1rem;
  font-weight: 700;
  color: #1976d2;
  letter-spacing: 0.04em;
  margin: 16px 0 6px 24px;
  text-shadow: 0 1px 2px rgba(25, 118, 210, 0.08);
}

.mat-nav-list {
  padding-top: 0;
}

.mat-list-item {
  border-radius: 8px;
  margin: 2px 8px;
  transition: background 0.18s, color 0.18s;
  color: #222;
  font-weight: 500;
  &:hover, &:focus {
    background: #e3eaf2;
    color: var(--mui-palette-primary-main, #1976d2);
  }
  &.active {
    background: #1976d2;
    color: #fff;
    font-weight: 700;
    .mat-icon {
      color: #fff;
    }
  }
}

.mat-icon {
  margin-right: 18px;
  color: var(--mui-palette-primary-main, #1976d2);
  font-variation-settings: 'wght' 400;
  font-size: 1.6rem;
  transition: color 0.18s;
}

.sidebar-business-switcher {
  padding: 0 16px 8px 16px;
  .business-switcher-field {
    width: 100%;
    margin: 0;
    ::ng-deep .mat-form-field-outline {
      border-radius: 8px;
    }
    ::ng-deep .mat-select-value {
      font-weight: 600;
      color: #1976d2;
    }
  }
}

.sidebar-sidenav {
  transition: width 0.2s cubic-bezier(0.4,0,0.2,1);
  &.sidebar-collapsed {
    width: 64px !important;
    min-width: 64px !important;
    max-width: 64px !important;
    .sidebar-nav {
      width: 64px !important;
      .sidebar-user-info, .sidebar-section-label, .sidebar-business-switcher, .sidebar-divider, span:not(.mat-icon), .sidebar-expand-icon {
        display: none !important;
      }
      .sidebar-avatar {
        margin: 0 auto;
      }
    }
  }
}

.sidebar-toggle-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 8px 0 0;
}

.sidebar-collapsible {
  .sidebar-expand-icon {
    margin-left: auto;
    transition: transform 0.18s;
  }
  .sidebar-submenu {
    padding-left: 32px;
    .sidebar-subitem {
      font-size: 0.97rem;
      .mat-icon {
        font-size: 1.2rem;
        margin-right: 12px;
      }
    }
  }
}