import { Component, Input } from '@angular/core';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { BusinessSelectorComponent, Business } from './business-selector/business-selector.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, MatListModule, MatIconModule, MatDividerModule, MatSelectModule, MatOptionModule, MatBadgeModule, BusinessSelectorComponent],
  template: `
    <nav class="sidebar-nav" aria-label="Main navigation">
      <!-- Company Logo Section -->
      <div class="sidebar-logo" *ngIf="!collapsed">
        <div class="logo-container">
          <div class="logo-text">S&E</div>
          <div class="logo-subtitle">JEWELERS</div>
        </div>
        <div class="company-name">S&E Jewelers</div>
      </div>

      <!-- Collapsed Logo -->
      <div class="sidebar-logo-collapsed" *ngIf="collapsed">
        <div class="logo-text-small">S&E</div>
      </div>

      <mat-divider class="sidebar-divider"></mat-divider>
      <mat-nav-list>
        <div class="sidebar-section-label" *ngIf="!collapsed">Main</div>
        <a mat-list-item routerLink="/dashboard" routerLinkActive="active" aria-label="Dashboard">
          <mat-icon>dashboard</mat-icon>
          <span *ngIf="!collapsed">Dashboard</span>
        </a>
        <a mat-list-item routerLink="/calendar" routerLinkActive="active" aria-label="Calendar">
          <mat-icon>calendar_month</mat-icon>
          <span *ngIf="!collapsed">Calendar</span>
        </a>
        <div class="sidebar-collapsible">
          <a mat-list-item (click)="toggleStaffMenu()" [class.active]="staffMenuOpen" aria-label="Staff">
            <mat-icon>people</mat-icon>
            <span *ngIf="!collapsed">Staff</span>
            <mat-icon class="sidebar-expand-icon" *ngIf="!collapsed">{{ staffMenuOpen ? 'expand_less' : 'expand_more' }}</mat-icon>
          </a>
          <div class="sidebar-submenu" *ngIf="staffMenuOpen && !collapsed">
            <a mat-list-item routerLink="/staff" routerLinkActive="active" class="sidebar-subitem" aria-label="Staff Directory">
              <mat-icon>list</mat-icon>
              <span>Directory</span>
            </a>
            <a mat-list-item routerLink="/staff/new" routerLinkActive="active" class="sidebar-subitem" aria-label="Add Staff">
              <mat-icon>person_add</mat-icon>
              <span>Add Staff</span>
            </a>
          </div>
        </div>
        <a mat-list-item routerLink="/tasks" routerLinkActive="active" [attr.aria-label]="taskBadgeCount + ' pending tasks'">
          <mat-icon [matBadge]="!collapsed ? taskBadgeCount : null"
            matBadgeColor="warn"
            [matBadgeHidden]="collapsed"
            aria-hidden="false"
          >assignment_turned_in</mat-icon>
          <span *ngIf="!collapsed">Tasks</span>
        </a>
        <a mat-list-item routerLink="/settings" routerLinkActive="active" aria-label="Settings">
          <mat-icon>settings</mat-icon>
          <span *ngIf="!collapsed">Settings</span>
        </a>
      </mat-nav-list>
      <mat-divider class="sidebar-divider"></mat-divider>
      <div class="sidebar-section-label" *ngIf="!collapsed">Other</div>
      <mat-nav-list>
        <a mat-list-item routerLink="/reports" routerLinkActive="active" aria-label="Reports">
          <mat-icon>bar_chart</mat-icon>
          <span *ngIf="!collapsed">Reports</span>
        </a>
        <a mat-list-item routerLink="/notifications" routerLinkActive="active" [attr.aria-label]="notificationBadgeCount + ' unread notifications'">
          <mat-icon [matBadge]="!collapsed ? notificationBadgeCount : null"
            matBadgeColor="warn"
            [matBadgeHidden]="collapsed"
            aria-hidden="false"
          >notifications</mat-icon>
          <span *ngIf="!collapsed">Notifications</span>
        </a>
      </mat-nav-list>
      <app-business-selector
        [businesses]="businesses"
        [selectedBusinessIds]="selectedBusinessIds"
        [collapsed]="collapsed"
        (selectionChange)="onBusinessSelectionChange($event)"
        style="margin-top:auto;"
      ></app-business-selector>
    </nav>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() collapsed = false;
  staffMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];
  businesses: Business[] = [
    { id: '1', name: 'Acme Corporation', logoUrl: '' },
    { id: '2', name: 'Globex Industries', logoUrl: '' },
    { id: '3', name: 'Stark Enterprises', logoUrl: '' }
  ];
  taskBadgeCount = 5;
  notificationBadgeCount = 3;
  toggleStaffMenu() {
    this.staffMenuOpen = !this.staffMenuOpen;
  }
  onBusinessSelectionChange(ids: string[]) {
    this.selectedBusinessIds = ids;
  }
}