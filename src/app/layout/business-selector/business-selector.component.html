<div
  class="business-selector"
  [class.collapsed]="collapsed"
  tabindex="0"
  role="button"
  [attr.aria-label]="ariaLabel"
  (click)="openSelectorDialog()"
  (keydown.enter)="openSelectorDialog()"
  (keydown.space)="openSelectorDialog()"
>
  <ng-container *ngIf="!collapsed; else collapsedView">
    <div class="logo-group">
      <ng-container *ngFor="let b of selectedBusinesses.slice(0, 3)">
        <img *ngIf="b.logoUrl" [src]="b.logoUrl" [alt]="b.name" class="business-logo" />
        <mat-icon *ngIf="!b.logoUrl" class="business-icon">business</mat-icon>
      </ng-container>
      <mat-chip *ngIf="isOneView && selectedBusinesses.length > 3" color="primary" class="plus-chip">
        +{{ selectedBusinesses.length - 3 }}
      </mat-chip>
    </div>
    <div class="business-info">
      <span class="business-name">
        {{ isOneView ? 'OneView: Multiple' : (selectedBusinesses[0].name || 'Select business') }}
      </span>
      <mat-chip *ngIf="isOneView" color="accent" class="oneview-chip">
        <mat-icon>visibility</mat-icon>
        OneView Active
      </mat-chip>
    </div>
  </ng-container>
  <ng-template #collapsedView>
    <button mat-icon-button aria-label="Select business">
      <mat-icon>business</mat-icon>
    </button>
  </ng-template>
</div>
