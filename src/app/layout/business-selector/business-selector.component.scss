@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

.business-selector {
  position: sticky;
  bottom: 0;
  width: 100%;
  background: rgba(25, 118, 210, 0.07);
  border-top: 2px solid #1976d2;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -2px 12px rgba(25, 118, 210, 0.08);
  display: flex;
  align-items: center;
  min-height: 56px;
  padding: 0 16px;
  cursor: pointer;
  z-index: 1201;
  transition: box-shadow 0.2s, background 0.2s;

  &.collapsed {
    justify-content: center;
    padding: 0;
  }

  &:focus {
    outline: 2px solid var(--mat-primary, #1976d2);
    outline-offset: -2px;
  }

  &:hover {
    background: rgba(25, 118, 210, 0.13);
    box-shadow: 0 -4px 24px rgba(25, 118, 210, 0.16);
  }

  .logo-group {
    display: flex;
    align-items: center;
    margin-right: 8px;

    .business-logo {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-left: -8px;
      border: 2px solid #fff;
      box-shadow: 0 1px 4px rgba(25, 118, 210, 0.10);
      background: linear-gradient(135deg, #1976d2 30%, #42a5f5 100%);
      object-fit: cover;
    }
    .business-icon {
      font-size: 32px;
      color: var(--mat-primary, #1976d2);
      margin-left: -8px;
      background: #f5f5f5;
      border-radius: 8px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .plus-chip {
      margin-left: 4px;
      font-weight: 600;
      height: 24px;
    }
  }

  .business-info {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    .business-name {
      font-weight: 700;
      color: #1976d2;
      letter-spacing: 0.01em;
      font-size: 1.08rem;
    }
    .oneview-chip {
      margin-left: 4px;
      font-weight: 600;
      font-size: 0.98rem;
      letter-spacing: 0.01em;
      background: linear-gradient(90deg, #1976d2 60%, #00bcd4 100%) !important;
      color: #fff !important;
    }
  }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.2);}
  100% { box-shadow: 0 0 8px 4px rgba(25, 118, 210, 0.12);}
}
