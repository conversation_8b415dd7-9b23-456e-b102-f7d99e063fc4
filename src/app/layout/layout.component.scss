.app-shell {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--sm-background-default);
}

.sidebar-sidenav {
  width: 240px;
  transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  background: var(--sm-background-paper);
  border-right: 1px solid var(--sm-border-color, #e0e0e0);
  box-shadow: var(--sm-shadow-light);

  &.sidebar-collapsed {
    width: 64px;
  }

  // Dark theme support
  .dark-theme & {
    background: var(--sm-background-paper);
    border-right-color: rgba(255, 255, 255, 0.1);
  }
}

.sidebar-toggle-btn {
  position: absolute;
  top: 12px;
  right: -20px;
  z-index: 1000;
  background: var(--sm-background-paper);
  border-radius: 50%;
  box-shadow: var(--sm-shadow-medium);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--sm-shadow-heavy);
    transform: scale(1.05);
  }

  button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--sm-primary-main);
    color: white;
    transition: all 0.2s ease;

    &:hover {
      background: var(--sm-primary-dark);
      transform: rotate(180deg);
    }

    mat-icon {
      transition: transform 0.2s ease;
    }
  }
}

.main-content {
  flex: 1;
  padding: 0;
  background: var(--sm-background-default);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  // Smooth scrolling
  scroll-behavior: smooth;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--sm-background-default);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--sm-text-secondary);
    border-radius: 4px;

    &:hover {
      background: var(--sm-text-primary);
    }
  }
}

// Responsive design
@media (max-width: 900px) {
  .sidebar-sidenav {
    width: 280px;

    &.sidebar-collapsed {
      width: 280px;
    }
  }

  .sidebar-toggle-btn {
    display: none;
  }

  .main-content {
    padding: 0;
  }
}

@media (max-width: 600px) {
  .sidebar-sidenav {
    width: 100vw;
  }
}

// Animation for smooth transitions
.mat-sidenav-container {
  background: var(--sm-background-default);
  color: var(--sm-text-primary);
}

.mat-sidenav-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Focus and accessibility improvements
.sidebar-toggle-btn button:focus {
  outline: 2px solid var(--sm-primary-main);
  outline-offset: 2px;
}

// Loading state support
.app-shell.loading {
  .main-content {
    opacity: 0.7;
    pointer-events: none;
  }
}