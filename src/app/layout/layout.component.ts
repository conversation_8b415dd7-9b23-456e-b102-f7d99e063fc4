import { Component, ViewChild } from '@angular/core';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarComponent } from './sidebar.component';
import { HeaderComponent } from './header.component';
import { MatSidenav } from '@angular/material/sidenav';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    SidebarComponent,
    HeaderComponent
  ],
  template: `
    <mat-sidenav-container class="app-shell">
      <mat-sidenav #drawer
        [mode]="isMobile ? 'over' : 'side'"
        [opened]="!collapsed"
        [fixedInViewport]="isMobile"
        [disableClose]="!isMobile"
        class="sidebar-sidenav"
        [class.sidebar-collapsed]="collapsed"
      >
        <div class="sidebar-toggle-btn">
          <button mat-icon-button (click)="toggleSidebar()" aria-label="Toggle sidebar">
            <mat-icon>{{ collapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
          </button>
        </div>
        <div [style.width]="collapsed ? '64px' : '240px'">
          <app-sidebar [collapsed]="collapsed"></app-sidebar>
        </div>
      </mat-sidenav>
      <mat-sidenav-content>
        <app-header (menuToggle)="drawer.toggle()"></app-header>
        <main class="main-content">
          <router-outlet></router-outlet>
        </main>
      </mat-sidenav-content>
    </mat-sidenav-container>
  `,
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent {
  collapsed = false;
  isMobile = false;
  @ViewChild('drawer') drawer!: MatSidenav;

  constructor() {
    if (typeof window !== 'undefined' && window.localStorage) {
      this.collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    }
    this.updateResponsive();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.updateResponsive.bind(this));
    }
  }

  updateResponsive() {
    this.isMobile = typeof window !== 'undefined' ? window.innerWidth < 900 : false;
    if (this.isMobile) {
      this.collapsed = false;
    }
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('sidebarCollapsed', String(this.collapsed));
    }
  }
} 