.app-header {
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  height: 64px;
  display: flex;
  align-items: center;
}

.app-title {
  flex: 1;
  text-align: center;
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
  font-family: 'Inter', 'Roboto', 'Open Sans', Arial, sans-serif;
}

.spacer {
  flex: 1;
}

.date-time-pill {
  display: flex;
  align-items: center;
  margin-left: 16px;
  padding: 4px 16px;
  border-radius: 20px;
  background: rgba(255,255,255,0.10);
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  opacity: 0.95;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  gap: 8px;
}

.icon-btn {
  margin: 0 4px;
  transition: transform 0.15s, background 0.15s;
  border-radius: 50%;
  &:hover, &:focus {
    transform: scale(1.1);
    background: rgba(255,255,255,0.12);
  }
  &:active {
    background: rgba(33,150,243,0.18);
  }
}

.user-avatar-btn {
  display: flex;
  align-items: center;
  .user-name {
    margin-left: 8px;
    font-weight: 600;
    font-size: 1rem;
    color: #fff;
    letter-spacing: 0.01em;
    font-family: 'Inter', 'Roboto', 'Open Sans', Arial, sans-serif;
    text-shadow: 0 1px 2px rgba(0,0,0,0.12);
  }
}

.mat-badge-content, .mat-badge-content.mat-badge-warn {
  font-weight: bold;
  font-size: 0.75rem;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  background: var(--mui-palette-error-main, #d32f2f);
  color: #fff;
  box-shadow: 0 1px 2px rgba(0,0,0,0.10);
}

.mat-menu-item .mat-icon {
  margin-right: 8px;
}

.date-time {
  margin-left: 16px;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  opacity: 0.85;
} 